DateTime reqPunchOutTime = DateTime(
  DateTime.now().year,
  DateTime.now().month,
  DateTime.now().day,
  18,
  00,
  00,
);

final Map<int, String> monthMap = const {
  1: 'January',
  2: 'February',
  3: 'March',
  4: 'April',
  5: 'May',
  6: 'June',
  7: 'July',
  8: 'August',
  9: 'September',
  10: 'October',
  11: 'November',
  12: 'December',
};

class Role {
  static const String designer = 'designer';
  static const String supervisor = 'supervisor';
  static const String admin = 'admin';
}

class ProjectStatus {
  static const String active = 'Active';
  static const String onHold = 'on-Hold';
  static const String finished = 'Finished';
}

class TaskStatus {
  static const String pending = 'Pending';
  static const String ongoing = 'Ongoing';
  static const String submitted = 'Submitted';
  static const String approved = 'Approved';
}

class TaskSPrioritytatus {
  static const String low = 'Low';
  static const String mid = 'Mid';
  static const String high = 'High';
}

class TaskTypes {
  static const String All = 'All';
  static const String mytask = 'My Task';
  static const String onGoing = 'Ongoing';
  static const String submitted = 'Submitted';
  static const String approved = 'Approved';
}

class AdminTaskTypes {
  static const String All = 'All';
  static const String completed = 'Completed';
  static const String onGoing = 'Ongoing';
}

class docTypes {
  static const String All = 'All';
  static const String PDF = 'Pdf';
  static const String Images = 'Images';
  static const String Others = 'Others';
}

class AttendanceFilter {
  static const String today = 'Today';
  static const String month = 'Month';
  static const String custom = 'Custom';
}

final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
