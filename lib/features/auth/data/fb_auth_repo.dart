import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/domain/repo/auth_repo.dart';

class FirebaseAuthRepo implements AuthRepo {
  @override
  Future<UserModel?> registerWithEmailPassword(
    String name,
    String email,
    String password,
    String role,
  ) async {
    try {
      final userCredential = await FBAuth.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      UserModel user = UserModel(
        docId: userCredential.user?.uid ?? '',
        name: name,
        email: email,
        role: role,
      );
      await FBFireStore.users.doc(user.docId).set(user.toJson());

      return user;
    } catch (e) {
      throw Exception("Registeration failed: $e");
    }
  }

  @override
  @override
  Future<UserModel?> loginWithEmailPassword(
    String email,
    String password,
  ) async {
    try {
      final userCredential = await FBAuth.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final uid = userCredential.user?.uid ?? '';
      final snapshot = await FBFireStore.users.doc(uid).get();

      if (!snapshot.exists) {
        throw Exception("User data not found in Firestore");
      }
      final userData = snapshot.data()!;

      return UserModel.fromJson(userData);
    } catch (e) {
      throw Exception("Login failed: $e");
    }
  }

  @override
  Future<void> forgetPassword(String email) async {
    try {
      print(email);
      await FBAuth.auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception("Failed to send password reset email: $e");
    }
  }

  @override
  Future<void> logout() async {
    await FBAuth.auth.signOut();
  }

  @override
  Future<void> updateProfie(String userId, String name, String role) async {
    await FBFireStore.users.doc(userId).update({'name': name, 'role': role});
  }

  @override
  Stream<bool> authStateStream() {
    return FBAuth.auth.authStateChanges().map((user) => user != null);
  }

  @override
  Stream<UserModel?> currentUserStream() {
    print("currentUserStream2");
    final user = FBAuth.auth.currentUser;
    if (user == null) return Stream.value(null);

    return FBFireStore.users.doc(user.uid).snapshots().map((doc) {
      if (!doc.exists) return null;
      return UserModel.fromJson(doc.data()!);
    });
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    final firebaseUser = FBAuth.auth.currentUser;

    if (firebaseUser == null) return null;

    final doc = await FBFireStore.users.doc(firebaseUser.uid).get();

    if (!doc.exists) return null;

    return UserModel.fromJson(doc.data()!);
  }
}
