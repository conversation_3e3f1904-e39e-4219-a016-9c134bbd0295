import 'dart:async';

import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/domain/repo/auth_repo.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/router.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepo authRepo;
  UserModel? _currentUser;

  StreamSubscription<bool>? authStream;
  StreamSubscription<UserModel?>? currentUserStream;

  AuthCubit({required this.authRepo}) : super(AuthState.initial()) {
    // // Auth status stream (login/logout)
    // authStream = authRepo.authStateStream().listen((isLoggedIn) {
    //   print("isLoggedIn: $isLoggedIn");
    //   emit(state.copyWith(isAuthenticated: isLoggedIn));
    // });

    // // User document changes stream
    // currentUserStream = authRepo.currentUserStream().listen((user) {
    //   _currentUser = user;
    //   emit(state.copyWith(currentUser: user, isAdmin: user?.role == "admin"));
    // });
  }

  void checkAuth() async {
    authStream = authRepo.authStateStream().listen((isLoggedIn) {
      print("isLoggedIn: $isLoggedIn");
      emit(state.copyWith(isAuthenticated: isLoggedIn));
    });
  }

  void checkUser() async {
    currentUserStream = authRepo.currentUserStream().listen((user) {
      _currentUser = user;
      emit(state.copyWith(currentUser: user, isAdmin: user?.role == "admin"));
    });
  }

  //register with email and password
  Future<void> register(
    String email,
    String password,
    String name,
    String role,
  ) async {
    try {
      emit(state.copyWith(loading: true, message: ""));
      await authRepo.registerWithEmailPassword(name, email, password, role);
      emit(
        state.copyWith(
          message: "New user register sucessfully",
          isRegistered: true,
          loading: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          message: e.toString(),
          isRegistered: false,
          loading: false,
        ),
      );
    }
  }

  //login with email and password
  Future<void> login(String email, String password) async {
    try {
      emit(state.copyWith(loading: true, message: ""));
      final user = await authRepo.loginWithEmailPassword(email, password);
      if (user != null) {
        _currentUser = user;
        emit(
          state.copyWith(
            isAdmin: user.role == "admin" ? true : false,
            loading: false,
            currentUser: user,
            isAuthenticated: true,
            message: "loggedin sucessfully",
          ),
        );
      } else {
        emit(
          state.copyWith(
            currentUser: null,
            isAuthenticated: false,
            loading: false,
            message: "login failed",
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isAuthenticated: false,
          message: e.toString(),
          loading: false,
        ),
      );
    }
  }

  //checkRole
  String checkRole() {
    return _currentUser?.role ?? "";
  }

  //forget password
  Future<void> forgetPassword(String email) async {
    emit(state.copyWith(loading: true, message: ""));
    try {
      await authRepo.forgetPassword(email);
      emit(
        state.copyWith(message: "Password reset email sent", loading: false),
      );
    } catch (e) {
      emit(state.copyWith(message: e.toString(), loading: false));
    }
  }

  //get current
  UserModel? get currentUser => _currentUser;

  //logout
  Future<void> logOut(BuildContext context) async {
    emit(state.copyWith(loading: true, message: ""));
    await authRepo.logout();
    emit(
      state.copyWith(
        isAuthenticated: false,
        loading: false,
        message: " Logged out successfully ",
      ),
    );
    context.go(Routes.login);
  }

  @override
  Future<void> close() {
    authStream?.cancel();
    return super.close();
  }
}
