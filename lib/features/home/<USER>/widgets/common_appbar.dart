import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class CommonAppBar extends StatelessWidget {
  CommonAppBar({super.key});
  @override
  Widget build(BuildContext context) {
    final authCubit = context.read<AuthCubit>();
    final size = MediaQuery.sizeOf(context).width;
    return AppBar(
      title: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          String userEmailInitial = "U"; // default placeholder

          if (state.isAuthenticated) {
            userEmailInitial = state.currentUser?.email[0].toUpperCase() ?? "";
          }

          return Row(
            children: [
              Image.asset(
                "assets/images/logo_light.png",
                color: Colors.black,
                width: 66,
                height: 33,
              ),
              Spacer(),

              InkWell(
                onTap: () {
                  context.push(Routes.masterTask);
                },
                child: Container(
                  padding: EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    color: AppColors.containerGreyColor,
                    border: Border.all(color: AppColors.borderGrey),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.checklist, size: 20),
                ),
              ),
              SizedBox(width: 10),
              authCubit.state.currentUser?.role == "admin"
                  ? BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
                    builder: (context, state) {
                      final attendanceAdminCubit =
                          context.read<AttendanceAdminCubit>();
                      return InkWell(
                        onTap: () {
                          context.push(Routes.request);
                        },

                        child: Badge(
                          label:
                              attendanceAdminCubit.state.requests.isNotEmpty
                                  ? Text(
                                    "${attendanceAdminCubit.state.requests.length}",
                                    style: TextStyle(color: Colors.white),
                                  )
                                  : Text("0"),

                          child: Icon(CupertinoIcons.bell),
                        ),
                      );
                    },
                  )
                  : SizedBox(),
              authCubit.state.currentUser?.role == "admin"
                  ? SizedBox(width: 10)
                  : SizedBox(),
              authCubit.state.currentUser?.role == "admin"
                  ? InkWell(
                    onTap: () {
                      kIsWeb
                          ? context.go(Routes.users)
                          : context.push(Routes.users);
                    },
                    child: Container(
                      padding: EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: AppColors.containerGreyColor,
                        border: Border.all(color: AppColors.borderGrey),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(CupertinoIcons.person_2, size: 20),
                    ),
                  )
                  : SizedBox(),
              authCubit.state.currentUser?.role == "admin"
                  ? SizedBox(width: 10)
                  : SizedBox(),
              InkWell(
                onTap: () {
                  context.push("${Routes.usersDetails}");
                },
                child: CircleAvatar(radius: 15, child: Text(userEmailInitial)),
              ),
            ],
          );
        },
      ),
      bottom: PreferredSize(
        child:
            size > 768
                ? Container(color: AppColors.borderGrey, height: 2)
                : SizedBox(),
        preferredSize: Size.fromHeight(4.0),
      ),
    );
  }
}
