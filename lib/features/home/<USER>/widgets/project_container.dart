import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/project_form.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ProjectContainer extends StatefulWidget {
  bool isMobile;
  ProjectContainer({super.key, required this.isMobile});

  @override
  State<ProjectContainer> createState() => _ProjectContainerState();
}

class _ProjectContainerState extends State<ProjectContainer> {
  @override
  void initState() {
    context.read<ProjectCubit>().fetchProjects();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProjectCubit, ProjectState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        // print("--");
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.projects.isEmpty) {
          return Center(child: Text("No Project Found"));
        } else if (state.projects.isNotEmpty) {
          return SingleChildScrollView(
            child: StaggeredGrid.extent(
              maxCrossAxisExtent: 530,
              mainAxisSpacing: 15,
              crossAxisSpacing: 30,
              children: [
                ...List.generate(state.projects.length, (index) {
                  final project = state.projects[index];
                  return GestureDetector(
                    onDoubleTap: () {
                      kIsWeb
                          ? showDialog(
                            context: context,
                            builder: (context) {
                              return Dialog(
                                child: Container(
                                  width: 800,
                                  child: BlocProvider(
                                    create:
                                        (context) => ProjectFormCubit(
                                          context.read<ProjectCubit>().repo,
                                        ),
                                    child: ProjectForm(editProject: project),
                                  ),
                                ),
                              );
                            },
                          )
                          : showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            useSafeArea: true,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.of(context).viewInsets,

                                child: BlocProvider(
                                  create:
                                      (context) => ProjectFormCubit(
                                        context.read<ProjectCubit>().repo,
                                      ),
                                  child: ProjectForm(editProject: project),
                                ),
                              );
                            },
                          );
                    },
                    onLongPress: () {
                      showConfirmDeletDialog(context, () {
                        // projectCubit.delete(project.docId);
                        context.read<ProjectCubit>().deleteProject(
                          project.docId,
                        );
                      });
                    },
                    onTap: () {
                      kIsWeb
                          ? context.go("${Routes.activity}/${project.docId}")
                          : context.push("${Routes.activity}/${project.docId}");
                    },
                    child: Container(
                      height: widget.isMobile ? null : 190,
                      padding: EdgeInsets.all(widget.isMobile ? 15 : 30),
                      decoration: BoxDecoration(
                        color: AppColors.containerGreyColor,
                        border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),

                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            // mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              project.projectImg != null
                                  ? ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: CachedNetworkImage(
                                      imageUrl: project.projectImg ?? "",
                                      height: 60,
                                      width: 60,
                                      fit: BoxFit.cover,
                                      placeholder:
                                          (context, url) => Container(
                                            width: 500,
                                            height: 500,
                                            alignment: Alignment.center,
                                            child:
                                                const CircularProgressIndicator(
                                                  color: AppColors.primary,
                                                  strokeWidth: 2,
                                                ),
                                          ),
                                      errorWidget:
                                          (context, url, error) => Container(
                                            width: 400,
                                            height: 400,
                                          ),
                                    ),
                                  )
                                  : Container(
                                    decoration: BoxDecoration(
                                      color: getColorFromInput(
                                        project.projectTitle[0],
                                      ),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    height: 60,
                                    width: 60,
                                    child: Center(
                                      child: Text(
                                        project.projectTitle[0].toUpperCase(),
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 30,
                                        ),
                                      ),
                                    ),
                                  ),

                              SizedBox(width: 15),

                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(project.projectTitle),
                                  SizedBox(height: 8),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 3,
                                      horizontal: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      color: statusColor(project.projectStatus),
                                    ),
                                    child: Text(
                                      project.projectStatus,
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Text(
                            truncateText(
                              project.projectDesc,
                              widget.isMobile ? 48 : 95,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }),
              ],
            ),
          );
        } else {
          return Center();
        }
      },
    );
  }
}
