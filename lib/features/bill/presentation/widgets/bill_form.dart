import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/presentation/cubit/billform_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class BillForm extends StatefulWidget {
  BillForm({super.key, required this.projectId, this.editBill});
  final BillModel? editBill;
  final String projectId;

  @override
  State<BillForm> createState() => _BillFormState();
}

class _BillFormState extends State<BillForm> {
  void initState() {
    super.initState();
    context.read<BillFormCubit>().initializeForm(widget.editBill);
  }

  @override
  Widget build(BuildContext context) {
    final billFormCubit = context.read<BillFormCubit>();

    return BlocConsumer<BillFormCubit, BillFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return IgnorePointer(
          ignoring: state.isLoading,
          child: Form(
            key: billFormCubit.formKey,
            child: StaggeredGrid.extent(
              maxCrossAxisExtent: 530,
              mainAxisSpacing: 15,
              crossAxisSpacing: 30,
              children: [
                ...List.generate(1, (index) {
                  return SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15,
                          vertical: 20,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              children: [
                                IconButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  icon: Icon(CupertinoIcons.xmark),
                                ),
                                Text(
                                  widget.editBill != null
                                      ? "Edit Bill"
                                      : "Add Bill",
                                  style: AppTextStyles.heading2,
                                ),
                                Spacer(),
                                PrimaryButton(
                                  isLoading: state.isLoading,
                                  text:
                                      widget.editBill != null
                                          ? "Update"
                                          : "Save",
                                  onPressed: () {
                                    billFormCubit.submit(
                                      editBill: widget.editBill,
                                      projectId: widget.projectId,
                                      context: context,
                                    );
                                  },

                                  height: 36,
                                ),
                              ],
                            ),
                            SizedBox(height: 10),
                            CustomTextField(
                              controller: billFormCubit.nameController,
                              hintText: "Bill Name *",
                              title: "",
                            ),
                            SizedBox(height: 10),
                            CustomNumTextField(
                              controller: billFormCubit.amountController,
                              hintText: "Bill Amount *",
                              title: "",
                            ),
                            SizedBox(height: 10),
                            CustomDescTextField(
                              controller: billFormCubit.descController,
                              hintText:
                                  "Bill Description (max 28 characters)  *",
                              title: "",
                              maxChars: 28,
                            ),
                            SizedBox(height: 10),
                            buildFilePreview(
                              context: context,
                              selectedFile: state.selectedFile,
                              dbFile: state.dbFile,
                              dbFileExt: widget.editBill?.billExtenstion,
                              dbFileName: widget.editBill?.billFileName,
                              isEdit: true,
                              onDelete: () {
                                final isDbFile =
                                    state.selectedFile == null &&
                                    state.dbFile != null;
                                context.read<BillFormCubit>().deletPickFile(
                                  isDbFile,
                                );
                              },
                              onView: () {
                                context.read<BillFormCubit>().viewPickFile(
                                  state.dbFile,
                                  context,
                                  widget.editBill?.billExtenstion,
                                );
                              },
                              isMessage: false,
                            ),
                            SizedBox(height: 10),
                            CustomFileUploadField(
                              hintText: "Upload Bill(image,pdf) *",
                              onTap:
                                  () async => billFormCubit.pickFile(context),
                              prefixIcon: Icon(CupertinoIcons.up_arrow),
                            ),
                            SizedBox(height: 10),
                            CustomFileUploadField(
                              hintText: "Take new photo",
                              onTap: () async {
                                billFormCubit.pickFileFromCamera(context);
                              },
                              prefixIcon: Icon(CupertinoIcons.camera),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }
}
