import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/bill/presentation/cubit/bill_cubit.dart';
import 'package:cp_associates/features/bill/presentation/cubit/billform_cubit.dart';
import 'package:cp_associates/features/bill/presentation/widgets/bill_detail_tile.dart';
import 'package:cp_associates/features/bill/presentation/widgets/bill_form.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BillPage extends StatefulWidget {
  final String projectId;
  BillPage({super.key, required this.projectId});

  @override
  State<BillPage> createState() => _BillPageState();
}

class _BillPageState extends State<BillPage> {
  @override
  Widget build(BuildContext context) {
    final billCubit = context.read<BillCubit>();
    return BlocConsumer<BillCubit, BillState>(
      listener: (context, state) {
        // print();
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return ResponsiveCustomBuilder(
          mobileBuilder: (width) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: filterTabs(context, state),
                ),
                SizedBox(height: 20),
                Expanded(
                  child: Stack(
                    children: [
                      BillDetailTile(projectId: widget.projectId),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Container(
                            alignment: Alignment.bottomRight,
                            child: AddBtn(
                              text: "upload",

                              onPressed: () {
                                kIsWeb
                                    ? showDialog(
                                      context: context,
                                      builder: (context) {
                                        return Dialog(
                                          child: Container(
                                            width: 500,
                                            child: BlocProvider(
                                              create:
                                                  (context) => BillFormCubit(
                                                    billCubit.repo,
                                                  ),
                                              child: BillForm(
                                                projectId: widget.projectId,
                                                editBill: null,
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    )
                                    : showModalBottomSheet(
                                      isScrollControlled: true,
                                      context: context,
                                      builder: (context) {
                                        return Padding(
                                          padding:
                                              MediaQuery.of(context).viewInsets,
                                          child: BlocProvider(
                                            create:
                                                (context) => BillFormCubit(
                                                  billCubit.repo,
                                                ),
                                            child: BillForm(
                                              projectId: widget.projectId,
                                              editBill: null,
                                            ),
                                          ),
                                        );
                                      },
                                    );
                              },
                              color: Color(0xff2BAC76),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 70),
              ],
            );
          },
          desktopBuilder: (width) {
            return Column(
              children: [
                filterTabs(context, state),
                SizedBox(height: 20),
                Expanded(
                  child: Stack(
                    children: [
                      BillDetailTile(projectId: widget.projectId),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Container(
                          alignment: Alignment.bottomRight,
                          child: AddBtn(
                            text: "upload",
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 500,
                                      child: BlocProvider(
                                        create:
                                            (context) =>
                                                BillFormCubit(billCubit.repo),
                                        child: BillForm(
                                          projectId: widget.projectId,
                                          editBill: null,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            color: Color(0xff2BAC76),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Row filterTabs(BuildContext context, BillState state) {
    return Row(
      children: [
        FilterContainer(
          title: "All",
          onFilterTap: () {
            context.read<BillCubit>().filterBillsByType(docTypes.All);
          },
          isSelected: state.selectedType == docTypes.All,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "PDF",
          onFilterTap: () {
            context.read<BillCubit>().filterBillsByType(docTypes.PDF);
          },
          isSelected: state.selectedType == docTypes.PDF,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Images",
          onFilterTap: () {
            context.read<BillCubit>().filterBillsByType(docTypes.Images);
          },
          isSelected: state.selectedType == docTypes.Images,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Others",
          onFilterTap: () {
            context.read<BillCubit>().filterBillsByType(docTypes.Others);
          },
          isSelected: state.selectedType == docTypes.Others,
        ),
      ],
    );
  }
}
