import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/domain/repo/auth_repo.dart';
import 'package:cp_associates/features/users/domain/repo/user_repo.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';

part 'user_state.dart';

class UserCubit extends Cubit<UserState> {
  final UserRepo userRepo;
  final AuthRepo authRepo;
  StreamSubscription<List<UserModel>>? userStream;
  UserCubit(this.userRepo, this.authRepo) : super(UserState.initial());

  void fetchAllUsers() {
    print("UserStream------");
    emit(state.copyWith(isLoading: true));
    userStream?.cancel();

    userStream = userRepo.getAllUsers().listen(
      (users) {
        print("UserStream------2");
        emit(state.copyWith(users: users, isLoading: false));
      },
      onError: (e) {
        emit(
          state.copyWith(
            message: "Failed to load Users: ${e.toString()}",
            isLoading: false,
          ),
        );
      },
    );
  }

  UserModel? getUserById(String userId) {
    try {
      final user = state.users.firstWhere((user) => user.docId == userId);
      return user;
    } catch (_) {
      return null;
    }
  }



}
