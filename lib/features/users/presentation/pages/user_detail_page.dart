import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/widgets/changepassword_form.dart';
import 'package:cp_associates/features/users/presentation/widgets/editprofile_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UserDetailPage extends StatefulWidget {
  const UserDetailPage({super.key});

  @override
  State<UserDetailPage> createState() => _UserDetailPageState();
}

class _UserDetailPageState extends State<UserDetailPage> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        // final user = context.read<AuthCubit>().currentUser;
        if (state.loading) {
          return Center(child: CircularProgressIndicator());
        }
        return ResponsiveWid(
          mobile: Scaffold(
            appBar: AppBar(title: Row(children: [Text("Profile")])),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 16,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 20,
                      ),
                      // padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppColors.containerGreyColor,
                        border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 35,
                            child: Text(
                              state.currentUser?.name[0].toUpperCase() ?? "",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 30,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          SizedBox(width: 20),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                state.currentUser?.name ?? "",
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(state.currentUser?.email ?? ""),
                              Text(state.currentUser?.role ?? ""),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 20,
                      ),
                      // padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppColors.containerGreyColor,
                        border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          profileTile(
                            icon: Icon(CupertinoIcons.person),
                            title: "Edit Profile",
                            onTap: () {
                              showModalBottomSheet(
                                isScrollControlled: true,
                                useSafeArea: true,
                                context: context,
                                builder: (context) {
                                  return BlocProvider(
                                    create:
                                        (context) => UserFormCubit(
                                          context.read<AuthCubit>().authRepo,
                                        ),
                                    child: Padding(
                                      padding:
                                          MediaQuery.of(context).viewInsets,
                                      child: EditProfileForm(),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                          SizedBox(height: 30),
                          profileTile(
                            icon: Icon(CupertinoIcons.lock),
                            title: "Change Password",
                            onTap: () {
                              showModalBottomSheet(
                                isScrollControlled: true,
                                useSafeArea: true,
                                context: context,
                                builder: (context) {
                                  return BlocProvider(
                                    create:
                                        (context) => UserFormCubit(
                                          context.read<AuthCubit>().authRepo,
                                        ),
                                    child: Padding(
                                      padding:
                                          MediaQuery.of(context).viewInsets,
                                      child: ChangePasswordForm(),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                          SizedBox(height: 30),
                          profileTile(
                            icon: Icon(CupertinoIcons.trash),
                            title: "Delete Account",
                            onTap: () {},
                          ),
                          SizedBox(height: 30),
                          profileTile(
                            icon: Icon(CupertinoIcons.square_arrow_right),
                            title: "Logout",
                            onTap: () {
                              showLogoutDialog(context, () {
                                context.read<AuthCubit>().logOut(context);
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget profileTile({
    required Icon icon,
    required String title,
    Function? onTap,
  }) {
    return InkWell(
      onTap: () => onTap!(),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppColors.containerGreyColor,
              border: Border.all(color: AppColors.borderGrey),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon.icon, size: 25),
          ),
          SizedBox(width: 15),
          Text(title, style: TextStyle(fontSize: 16)),
          Spacer(),
          Icon(CupertinoIcons.right_chevron, size: 15),
        ],
      ),
    );
  }
}
