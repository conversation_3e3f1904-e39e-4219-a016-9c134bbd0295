import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/attendances/domain/antity/punches_model.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class UserTile extends StatelessWidget {
  const UserTile({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserCubit, UserState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
            builder: (attendnaceContext, attendanceState) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    ...List.generate(state.users.length, (index) {
                      final user = state.users[index];
                      final userPunches =
                          attendanceState.punches
                              .where((p) => p.userId == user.docId)
                              .toList()
                            ..sort(
                              (a, b) => b.createdAt.compareTo(a.createdAt),
                            ); // Sort descending by createdAt

                      final userPunch =
                          userPunches.isNotEmpty
                              ? userPunches
                                  .first // latest punch
                              : PunchesModel(
                                docId: '',
                                userId: '',
                                punchIn: false,
                                createdAt: DateTime.now(),
                              );

                      final punchStatus =
                          userPunch.docId.isEmpty
                              ? "No Record"
                              : userPunch.punchIn
                              ? "Punched In"
                              : "Punched Out";

                      return InkWell(
                        onTap: () {
                          context.push(
                            "${Routes.attendanceDetail}/${user.docId}",
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 15),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 10,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.borderGrey),
                              borderRadius: BorderRadius.circular(10),
                              color: AppColors.containerGreyColor,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,

                                    border: Border.all(
                                      width: 2,
                                      color:
                                          punchStatus == "Punched In"
                                              ? const Color.fromARGB(
                                                255,
                                                0,
                                                240,
                                                8,
                                              )
                                              : Colors.red,
                                    ),
                                  ),
                                  child: CircleAvatar(
                                    backgroundColor: getColorFromInput(
                                      user.name[0],
                                    ),
                                    radius: 25,
                                    child: Text(
                                      user.name[0].toUpperCase(),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 30,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(width: 10),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("${user.name} (${user.role})"),
                                    Text(user.email),
                                  ],
                                ),

                                Spacer(),
                                // Text(punchStatus),
                                // SizedBox(width: 10),
                                Icon(CupertinoIcons.right_chevron, size: 15),
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
