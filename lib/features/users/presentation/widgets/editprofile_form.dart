import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfields.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_form_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EditProfileForm extends StatefulWidget {
  const EditProfileForm({super.key});

  @override
  State<EditProfileForm> createState() => _EditProfileFormState();
}

class _EditProfileFormState extends State<EditProfileForm> {
  @override
  void initState() {
    final userCubit = context.read<UserCubit>();
    final userFormCubit = context.read<UserFormCubit>();
    userFormCubit.nameController.text =
        context.read<AuthCubit>().currentUser?.name ?? "";
    userFormCubit.emailController.text =
        context.read<AuthCubit>().currentUser?.email ?? "";
    userFormCubit.selectRole(context.read<AuthCubit>().currentUser?.role ?? "");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final userFormCubit = context.read<UserFormCubit>();
    return BlocConsumer<UserFormCubit, UserFormState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      builder: (context, state) {
        return SingleChildScrollView(
          child: IgnorePointer(
            ignoring: state.isLoading,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(CupertinoIcons.xmark),
                      ),
                      Text("Edit Profile", style: AppTextStyles.formtitle),
                      Spacer(),
                    ],
                  ),
                  CustomTextField(
                    controller: userFormCubit.nameController,
                    hintText: "enter your name",
                    title: "Name",
                  ),
                  SizedBox(height: 20),
                  IgnorePointer(
                    child: CustomTextField(
                      controller: userFormCubit.emailController,
                      hintText: "enter your email",
                      title: "Email",
                    ),
                  ),
                  SizedBox(height: 20),
                  Text("Role"),
                  SizedBox(height: 5),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField(
                          value: userFormCubit.state.selectedRole,
                          decoration: InputDecoration(
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: AppColors.grey2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            hintText: "Role",
                            hintStyle: TextStyle(color: AppColors.grey2),

                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          items: const [
                            DropdownMenuItem(
                              value: Role.designer,
                              child: Text(Role.designer),
                            ),
                            DropdownMenuItem(
                              value: Role.supervisor,
                              child: Text(Role.supervisor),
                            ),
                          ],
                          onChanged: (value) {
                            userFormCubit.selectRole(value ?? "");
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: PrimaryButton(
                          isLoading: state.isLoading,
                          text: "SAVE",
                          onPressed: () {
                            context.read<UserFormCubit>().updateProfile(
                              FBAuth.auth.currentUser?.uid ?? "",
                              userFormCubit.nameController.text,
                              userFormCubit.state.selectedRole,
                              context,
                            );
                          },

                          height: 36,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
