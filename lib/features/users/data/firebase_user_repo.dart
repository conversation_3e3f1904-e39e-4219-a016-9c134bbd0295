import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/users/domain/repo/user_repo.dart';

class FirebaseUserRepo extends UserRepo {
  final usersRef = FBFireStore.users;
  @override
  Stream<List<UserModel>> getAllUsers() {
    return usersRef.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => UserModel.fromSnap(doc)).toList();
    });
  }
  
}
