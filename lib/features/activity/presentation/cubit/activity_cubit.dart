import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/domain/repo/activity_repo.dart';
import 'package:flutter/material.dart';

part 'activity_state.dart';

class ActivityCubit extends Cubit<ActivityState> {
  final ActivityRepo activityRepo;
  ActivityCubit(this.activityRepo) : super(ActivityState.initial());
  StreamSubscription<List<ActivityModel>>? activityStream;

  void viewActivity(ActivityModel activity, BuildContext context) async {
    await viewFile(
      context: context,
      selectedFile: null,
      dbImg: activity.attachment,
      dbImgExt: activity.attachmentType,
    );
  }

  void fetchActivities(String projectId) {
    emit(state.copyWith(isLoading: true, message: ""));
    print("fetchActivities----1");

    activityStream?.cancel();

    activityStream = activityRepo
        .fetchProjectActivity(projectId)
        .listen(
          (activities) {
            print("fetchActivities----2");
            activities.sort((a, b) => a.sendAt.compareTo(b.sendAt));
            emit(
              state.copyWith(
                activities: activities,
                isLoading: false,
                message: "",
              ),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch activities: ${error.toString()}",
              ),
            );
          },
        );
  }

  @override
  Future<void> close() {
    activityStream?.cancel();
    return super.close();
  }
}
