import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_form_cubit.dart';
import 'package:cp_associates/features/activity/presentation/widget/activity_detail_tile.dart';
import 'package:cp_associates/features/activity/presentation/widget/activity_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActivityPage extends StatefulWidget {
  ActivityPage({super.key, required this.projectId});
  final String? projectId;

  @override
  State<ActivityPage> createState() => _ActivityPageState();
}

class _ActivityPageState extends State<ActivityPage> {
  @override
  void initState() {
    super.initState();
    context.read<ActivityCubit>().fetchActivities(widget.projectId ?? "");
  }

  @override
  Widget build(BuildContext context) {
    final activityCubit = context.read<ActivityCubit>();
    return ResponsiveCustomBuilder(
      mobileBuilder: (width) {
        return Stack(
          fit: StackFit.expand,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 50),

              child: ActivityDetailTile(projectId: widget.projectId ?? ""),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 90),
              child: Align(
                alignment: Alignment.bottomRight,
                child: AddBtn(
                  text: "acivity",
                  onPressed: () {
                    showModalBottomSheet(
                      isScrollControlled: true,
                      useSafeArea: true,
                      context: context,
                      builder: (context) {
                        return Padding(
                          padding: MediaQuery.of(context).viewInsets,

                          child: BlocProvider(
                            create:
                                (context) => ActivityFormCubit(
                                  activityCubit.activityRepo,
                                ),
                            child: ActivityForm(
                              projectId: widget.projectId ?? "",
                            ),
                          ),
                        );
                      },
                    );
                  },
                  color: AppColors.secondary,
                ),
              ),
            ),
          ],
        );
      },
      desktopBuilder: (width) {
        return Stack(
          children: [
            ActivityDetailTile(projectId: widget.projectId ?? ""),
            Padding(
              padding: const EdgeInsets.only(bottom: 0),
              child: Align(
                alignment: Alignment.bottomRight,
                child: AddBtn(
                  text: "acivity",
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return Dialog(
                          child: Container(
                            width: 500,
                            child: BlocProvider(
                              create:
                                  (context) => ActivityFormCubit(
                                    activityCubit.activityRepo,
                                  ),
                              child: ActivityForm(
                                projectId: widget.projectId ?? "",
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                  color: AppColors.secondary,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
