import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_form_cubit.dart';
import 'package:cp_associates/features/admin/presentation/widget/admin_task_form.dart';
import 'package:cp_associates/features/admin/presentation/widget/admintask_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AdminTasks extends StatefulWidget {
  const AdminTasks({super.key});

  @override
  State<AdminTasks> createState() => _AdminTasksState();
}

class _AdminTasksState extends State<AdminTasks> {
  @override
  void initState() {
    super.initState();
    final cubit = context.read<AdminTaskCubit>();
    // cubit.fetchAllAdminTask();
    cubit.filterTask(AdminTaskTypes.onGoing); // <- Add this
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdminTaskCubit, AdminTaskState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(title: Text("Admin Tasks")),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            // padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                filterTabs(context, state),
                SizedBox(height: 20),
                state.selectedType == AdminTaskTypes.completed
                    ? Row(
                      children: [
                        Expanded(
                          child: DropdownButtonHideUnderline(
                            child: DropdownButtonFormField<int>(
                              decoration: InputDecoration(
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: AppColors.grey2,
                                  ),
                                ),
                                hintText: "Select Month",
                                hintStyle: AppTextStyles.hintText,

                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              value: state.selectedMonth,
                              onChanged: (value) {
                                if (value != null) {
                                  context.read<AdminTaskCubit>().selectMonth(
                                    value,
                                  );
                                  context
                                      .read<AdminTaskCubit>()
                                      .fetchCompletedAdminTask(
                                        DateTime(DateTime.now().year, value),
                                      );
                                }
                              },
                              items: List.generate(12, (index) {
                                final monthIndex = index + 1;

                                return DropdownMenuItem(
                                  value: monthIndex,
                                  child: Text(monthMap[monthIndex] ?? ""),
                                );
                              }),
                            ),
                          ),
                        ),
                      ],
                    )
                    : SizedBox(),
                SizedBox(
                  height:
                      state.selectedType == AdminTaskTypes.completed ? 20 : 0,
                ),
                Expanded(
                  child: Stack(
                    children: [
                      AdminTaskTile(),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Container(
                            alignment: Alignment.bottomRight,
                            child: AddBtn(
                              text: "Add Task",

                              onPressed: () {
                                showModalBottomSheet(
                                  isScrollControlled: true,
                                  context: context,
                                  builder: (context) {
                                    return Padding(
                                      padding:
                                          MediaQuery.of(context).viewInsets,
                                      child: BlocProvider(
                                        create:
                                            (context) => AdminTaskFormCubit(
                                              context
                                                  .read<AdminTaskCubit>()
                                                  .adminTaskRepo,
                                            ),
                                        child: AdminTaskForm(
                                          editAdminTask: null,
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              color: AppColors.secondary,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 30),
              ],
            ),
          ),
        );
      },
    );
  }

  Row filterTabs(BuildContext context, AdminTaskState state) {
    return Row(
      children: [
        FilterContainer(
          title: "Ongoing",
          onFilterTap: () {
            context.read<AdminTaskCubit>().filterTask(AdminTaskTypes.onGoing);
          },
          isSelected: state.selectedType == AdminTaskTypes.onGoing,
        ),
        SizedBox(width: 10),
        FilterContainer(
          title: "Completed",
          onFilterTap: () {
            context.read<AdminTaskCubit>().filterTask(AdminTaskTypes.completed);
          },
          isSelected: state.selectedType == AdminTaskTypes.completed,
        ),
        SizedBox(width: 10),
      ],
    );
  }
}
