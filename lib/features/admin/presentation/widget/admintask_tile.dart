import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_form_cubit.dart';
import 'package:cp_associates/features/admin/presentation/pages/admin_task_detail.dart';
import 'package:cp_associates/features/admin/presentation/widget/admin_task_form.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AdminTaskTile extends StatefulWidget {
  const AdminTaskTile({super.key});

  @override
  State<AdminTaskTile> createState() => _AdminTaskTileState();
}

class _AdminTaskTileState extends State<AdminTaskTile> {
  void initState() {
    context.read<AdminTaskCubit>().fetchAllAdminTask();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    context.read<AdminTaskCubit>();
    return BlocConsumer<AdminTaskCubit, AdminTaskState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.adminTasks.isEmpty) {
          return const Center(child: Text("No Admin Task Avaliable"));
        } else {
          final filteredAdminTasks =
              state.filteredAdminTasks.isEmpty
                  ? state.adminTasks
                  : state.filteredAdminTasks;
          return SingleChildScrollView(
            child: Column(
              spacing: 15,
              mainAxisSize: MainAxisSize.min,
              children: [
                // filterTabs(context, state),
                ...List.generate(filteredAdminTasks.length, (index) {
                  final adminTask = filteredAdminTasks[index];
                  ProjectModel? project = context
                      .read<ProjectCubit>()
                      .fetchProjectById(adminTask.projectId ?? "");

                  return InkWell(
                    onDoubleTap: () {
                      showModalBottomSheet(
                        isScrollControlled: true,
                        context: context,
                        builder: (context) {
                          return Padding(
                            padding: MediaQuery.of(context).viewInsets,
                            child: BlocProvider(
                              create:
                                  (context) => AdminTaskFormCubit(
                                    context
                                        .read<AdminTaskCubit>()
                                        .adminTaskRepo,
                                  ),
                              child: AdminTaskForm(editAdminTask: adminTask),
                            ),
                          );
                        },
                      );
                    },
                    onTap: () {
                      showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        context: context,
                        builder: (context) {
                          return Padding(
                            padding: MediaQuery.of(context).viewInsets,
                            child: AdminTaskDetail(
                              adminTask: adminTask,
                              project: project,
                            ),
                          );
                        },
                      );
                    },
                    onLongPress: () {
                      showConfirmDeletDialog(context, () {
                        context.read<AdminTaskCubit>().deleteAdminTask(
                          adminTask.docId,
                        );
                      });
                    },
                    child: Container(
                      // height: widget.isMobile ? null : 190,
                      padding: EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        color: AppColors.containerGreyColor,
                        border: Border.all(color: AppColors.borderGrey),
                        borderRadius: BorderRadius.circular(12),
                      ),

                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            // mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: getColorFromInput(
                                    project != null
                                        ? project.projectTitle[0]
                                        : adminTask.title[0],
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                height: 45,
                                width: 45,
                                child: Center(
                                  child:
                                      project != null
                                          ? Text(
                                            project.projectTitle[0]
                                                .toUpperCase(),
                                            // "test",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 15,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          )
                                          : Text(
                                            adminTask.title[0].toUpperCase(),
                                            // "test",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 15,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                ),
                              ),
                              SizedBox(width: 15),

                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    project?.projectTitle ?? adminTask.title,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),

                                  project != null
                                      ? Text(
                                        adminTask.title,
                                        style: TextStyle(
                                          fontSize: 14,
                                          // fontWeight: FontWeight.w600,
                                        ),
                                      )
                                      : Text(
                                        adminTask.desc,
                                        style: TextStyle(
                                          fontSize: 14,
                                          // fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                ],
                              ),
                              Spacer(),
                              Icon(CupertinoIcons.chevron_right, size: 20),
                            ],
                          ),
                          // SizedBox(height: 10),
                          // Text(truncateText(adminTask.desc, 48)),
                        ],
                      ),
                    ),
                  );
                }),
                SizedBox(height: 30),
              ],
            ),
          );
        }
      },
    );
  }
}
