import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/task/presentation/pages/task_detail_page.dart';
import 'package:cp_associates/features/task/presentation/widgets/task_form.dart';
import 'package:cp_associates/features/task/presentation/widgets/task_header_detail.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class TaskDetailTile extends StatefulWidget {
  String projectId;
  TaskDetailTile({super.key, required this.projectId});

  @override
  State<TaskDetailTile> createState() => _TaskDetailTileState();
}

class _TaskDetailTileState extends State<TaskDetailTile> {
  @override
  Widget build(BuildContext context) {
    final taskCubit = context.read<TaskCubit>();
    return BlocConsumer<TaskCubit, TaskState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state.task.isNotEmpty) {
          List<TaskModel> filteredtasks = [];
          if (state.selectedType == TaskTypes.All) {
            filteredtasks = state.task;
          } else if (state.selectedType == TaskTypes.mytask) {
            filteredtasks =
                state.task.where((task) {
                  return task.assignTo.contains(
                    FBAuth.auth.currentUser?.uid ?? "",
                  );
                }).toList();
          } else if (state.selectedType == TaskTypes.onGoing) {
            filteredtasks =
                state.task.where((task) {
                  return task.status.contains(TaskTypes.onGoing);
                }).toList();
          } else if (state.selectedType == TaskTypes.submitted) {
            filteredtasks =
                state.task.where((task) {
                  return task.status.contains(TaskTypes.submitted);
                }).toList();
          } else {
            filteredtasks =
                state.task.where((task) {
                  return task.status.contains(TaskTypes.approved);
                }).toList();
          }

          return filteredtasks.isEmpty
              ? Center(child: Text("No Task Avaliable"))
              : SingleChildScrollView(
                child: Column(
                  spacing: 15,
                  children: [
                    ...List.generate(filteredtasks.length, (index) {
                      final task = filteredtasks[index];

                      return GestureDetector(
                        onLongPress: () {
                          showConfirmDeletDialog(
                            context,
                            () => taskCubit.deletTask(task.docId),
                          );
                        },
                        onTap: () {
                          kIsWeb
                              ? showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      constraints: BoxConstraints(
                                        maxWidth: 500,
                                      ),
                                      child: BlocProvider(
                                        create:
                                            (context) => TaskFormCubit(
                                              taskCubit.taskRepo,
                                            ),
                                        child: TaskDetailPage(
                                          taskId: task.docId,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              )
                              : context.push(
                                "${Routes.taskDetail}/${task.docId}",
                              );
                        },
                        onDoubleTap: () {
                          kIsWeb
                              ? showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                    child: Container(
                                      width: 500,
                                      child: BlocProvider(
                                        create:
                                            (context) => TaskFormCubit(
                                              taskCubit.taskRepo,
                                            ),
                                        child: TaskForm(
                                          projectId: widget.projectId,
                                          editTask: task,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              )
                              : showModalBottomSheet(
                                useSafeArea: true,
                                isScrollControlled: true,
                                context: context,
                                builder: (context) {
                                  return Padding(
                                    padding: MediaQuery.of(context).viewInsets,
                                    child: BlocProvider(
                                      create:
                                          (context) =>
                                              TaskFormCubit(taskCubit.taskRepo),
                                      child: TaskForm(
                                        projectId: widget.projectId,
                                        editTask: task,
                                      ),
                                    ),
                                  );
                                },
                              );
                        },
                        child: Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: AppColors.containerGreyColor,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: AppColors.borderGrey),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TaskHeaderDetail(task: task),
                              SizedBox(height: 5),
                              Text(task.desc),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              );
        } else if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else {
          return Center(child: Text("No Task Avaliable"));
        }
      },
    );
  }
}
