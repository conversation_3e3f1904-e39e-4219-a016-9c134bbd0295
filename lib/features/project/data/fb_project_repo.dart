import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';

class FirebaseProjectRepo implements ProjectRepo {
  final projectsRef = FBFireStore.projects;

  @override
  Future<void> createProject(ProjectModel project) async {
    final docRef = projectsRef.doc();
    final newProject = project.copyWith(docId: docRef.id);
    await docRef.set(newProject.toJson());
  }

  @override
  Future<void> updateProject(ProjectModel project) async {
    await projectsRef.doc(project.docId).update(project.toJson());
  }

  @override
  Future<void> deleteProject(String docId) async {
    await projectsRef.doc(docId).delete();
  }

  @override
  Stream<List<ProjectModel>> getAllProjects() {
    return projectsRef.snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => ProjectModel.fromSnapshot(doc))
          .toList();
    });
  }

  @override
  Future<ProjectModel?> getProjectById(String id) async {
    final doc = await projectsRef.doc(id).get();

    if (!doc.exists) return null;

    return ProjectModel.fromJson(doc.data()!);
  }
}
