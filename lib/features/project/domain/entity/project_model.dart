import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cp_associates/features/project/domain/entity/transcation_model.dart';

class ProjectModel {
  String docId;
  String projectTitle;
  String projectDesc;
  String projectAddress;
  String projectStatus;
  DateTime projectStatusUpdateAt;
  String? projectImg;
  String? projectImgExt;
  String? projectImgName;
  String clientName;
  String clientContact;
  num totalAmount;
  String createdBy;
  DateTime? createdAt;
  DateTime? completedAt;
  List<String> userId;
  List<TranscationModel>? transcations;

  ProjectModel({
    required this.docId,
    required this.projectTitle,
    required this.projectDesc,
    required this.projectAddress,
    required this.projectStatus,
    required this.projectStatusUpdateAt,
    this.projectImg,
    this.projectImgExt,
    this.projectImgName,
    required this.clientName,
    required this.clientContact,
    required this.totalAmount,
    required this.createdAt,
    required this.completedAt,
    required this.userId,
    required this.createdBy,
    required this.transcations,
  });

  ProjectModel copyWith({
    String? docId,
    String? projectTitle,
    String? projectDesc,
    String? projectAddress,
    String? projectStatus,
    String? projectImg,
    String? projectImgExt,
    String? projectImgName,
    DateTime? projectStatusUpdateAt,
    String? clientName,
    String? clientContact,
    num? totalAmount,
    DateTime? createdAt,
    DateTime? completedAt,
    List<String>? userId,
    String? createdBy,
    List<TranscationModel>? transcations,
  }) {
    return ProjectModel(
      docId: docId ?? this.docId,
      projectTitle: projectTitle ?? this.projectTitle,
      projectDesc: projectDesc ?? this.projectDesc,
      projectAddress: projectAddress ?? this.projectAddress,
      projectStatus: projectStatus ?? this.projectStatus,
      projectStatusUpdateAt:
          projectStatusUpdateAt ?? this.projectStatusUpdateAt,
      projectImg: projectImg ?? this.projectImg,
      projectImgExt: projectImgExt ?? this.projectImgExt,
      projectImgName: projectImgName ?? this.projectImgName,
      clientName: clientName ?? this.clientName,
      clientContact: clientContact ?? this.clientContact,
      totalAmount: totalAmount ?? this.totalAmount,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      userId: userId ?? this.userId,
      createdBy: createdBy ?? this.createdBy,
      transcations: transcations ?? this.transcations,
    );
  }

  factory ProjectModel.fromSnapshot(DocumentSnapshot doc) {
    final json = doc.data() as Map<String, dynamic>;
    return ProjectModel.fromJson({...json, 'docId': doc.id});
  }

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      docId: json['docId'] ?? '',
      projectTitle: json['projectTitle'] ?? '',
      projectDesc: json['projectDesc'] ?? '',
      projectAddress: json['projectAddress'] ?? '',
      projectStatus: json['projectStatus'] ?? '',
      projectStatusUpdateAt:
          (json['projectStatusUpdateAt'] as Timestamp).toDate(),
      projectImg: json['projectImg'],
      projectImgExt: json['projectImgExt'],
      projectImgName: json['projectImgName'],
      clientName: json['clientName'] ?? '',
      clientContact: json['clientContact'] ?? '',
      totalAmount: json['totalAmount'] ?? 0,
      createdAt:
          json['createdAt'] != null
              ? (json['createdAt'] as Timestamp).toDate()
              : null,
      completedAt:
          json['completedAt'] != null
              ? (json['completedAt'] as Timestamp).toDate()
              : null,
      userId: List<String>.from(json['userId'] ?? []),
      createdBy: json['createdBy'] ?? '',
      transcations:
          (json['transcations'] as List)
              .map((e) => TranscationModel.fromJson(e))
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'projectTitle': projectTitle,
      'projectDesc': projectDesc,
      'projectAddress': projectAddress,
      'projectStatus': projectStatus,
      'projectStatusUpdateAt': projectStatusUpdateAt,
      'projectImg': projectImg,
      'projectImgExt': projectImgExt,
      'projectImgName': projectImgName,
      'clientName': clientName,
      'clientContact': clientContact,
      'totalAmount': totalAmount,
      'createdAt': createdAt,
      'completedAt': completedAt,
      'userId': userId,
      'createdBy': createdBy,
      'transcations': transcations?.map((e) => e.toJson()).toList(),
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'docId': docId,
      'projectTitle': projectTitle,
      'projectDesc': projectDesc,
      'projectAddress': projectAddress,
      'projectStatus': projectStatus,
      'projectStatusUpdateAt': projectStatusUpdateAt,
      'projectImg': projectImg,
      'projectImgExt': projectImgExt,
      'projectImgName': projectImgName,
      'clientName': clientName,
      'clientContact': clientContact,
      'totalAmount': totalAmount,
      'createdAt': createdAt,
      'completedAt': completedAt,
      'userId': userId,
      'createdBy': createdBy,
      'transcations': transcations?.map((e) => e.toJson()).toList(),
    };
  }
}
