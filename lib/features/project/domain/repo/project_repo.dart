import 'package:cp_associates/features/project/domain/entity/project_model.dart';

abstract class ProjectRepo {
  Future<void> createProject(ProjectModel project);
  Future<void> updateProject(ProjectModel project);
  Future<void> deleteProject(String docId);
  // Future<List<ProjectModel>> getAllProjects();
  Stream<List<ProjectModel>> getAllProjects();
  Future<ProjectModel?> getProjectById(String id);
}
