import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_bottomsheet.dart';
import 'package:cp_associates/features/task/presentation/pages/task_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../activity/presentation/pages/activity_page.dart';
import '../../../bill/presentation/pages/bill_page.dart';
import '../../../document/presentation/pages/document_page.dart';

class MobileProjectLayout extends StatefulWidget {
  final String projectId;
  // final String currentTab;
  MobileProjectLayout({
    super.key,
    required this.projectId,
    // required this.currentTab,
  });

  @override
  State<MobileProjectLayout> createState() => _MobileProjectLayoutState();
}

class _MobileProjectLayoutState extends State<MobileProjectLayout> {
  int _selectedIndex = 0;
  final PageController pageController = PageController(initialPage: 0);

  void initState() {
    super.initState();

    context.read<ProjectCubit>().fetchProjectByIdForDetail(widget.projectId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProjectCubit, ProjectState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Scaffold(body: Center(child: CircularProgressIndicator()));
        } else if (state.projectDetail != null) {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: true,
              title: GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,

                    builder: (context) {
                      return ProjectDetailBottomsheet();
                    },
                  );
                },
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: getColorFromInput(
                          state.projectDetail?.projectTitle[0] ?? "",
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      height: 30,
                      width: 30,
                      child: Center(
                        child: Text(
                          state.projectDetail?.projectTitle[0].toUpperCase() ??
                              "",
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                    ),
                    SizedBox(width: 20),
                    Text(
                      state.projectDetail?.projectTitle ?? "",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            extendBody: true,
            body: Padding(
              padding: const EdgeInsets.all(15.0),
              child: PageView(
                allowImplicitScrolling: false,
                physics: NeverScrollableScrollPhysics(),
                controller: pageController,
                children: <Widget>[
                  Center(child: ActivityPage(projectId: widget.projectId)),
                  Center(child: TaskPage(projectId: widget.projectId)),
                  Center(child: DocumentPage(projectId: widget.projectId)),
                  Center(child: BillPage(projectId: widget.projectId)),
                ],
              ),
            ),

            bottomNavigationBar: BottomNavigationBar(
              type: BottomNavigationBarType.fixed,
              selectedItemColor: AppColors.primary,
              unselectedItemColor: AppColors.grey2,
              showUnselectedLabels: true,

              currentIndex: _selectedIndex,
              elevation: 0,
              onTap: (index) {
                setState(() {
                  _selectedIndex = index;
                  pageController.jumpToPage(index);
                });
              },

              items: [
                BottomNavigationBarItem(
                  icon: Icon(Icons.chat_bubble_outline),
                  label: "Activity",
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.checklist),
                  label: "Task",
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.attach_file),
                  label: "Document",
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.receipt_long),
                  label: "Bill",
                ),
              ],
            ),
          );
        } else {
          return Scaffold();
        }
      },
    );
  }
}
