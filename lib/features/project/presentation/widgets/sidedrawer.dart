import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SideDrawer extends StatefulWidget {
  const SideDrawer({super.key, required this.projectId});
  final String projectId;

  @override
  State<SideDrawer> createState() => _SideDrawerState();
}

class _SideDrawerState extends State<SideDrawer> {
  int selectedIndex = -1;
  bool isShow = true;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // height: double.infinity, // Makes the sidebar full height
      child: Container(
        width: 280,
        color: AppColors.secondary,
        // decoration: BoxDecoration(
        //   gradient: LinearGradient(
        //     colors: [AppColors.primary, AppColors.secondary],
        //   ),
        // ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment:
              MainAxisAlignment.spaceBetween, // Push logout to bottom
          children: [
            const Padding(
              padding: EdgeInsets.only(top: 30),
              child: Image(
                image: AssetImage("assets/images/logo_light.png"),
                color: Colors.black,
                width: 120,
                height: 60,
              ),
            ),
            SizedBox(height: 40),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),
                    const SizedBox(height: 20),
                    tabs(
                      Icons.chat_bubble_outline,
                      'Activity',
                      0,
                      "${Routes.activity}/${widget.projectId}",
                    ),
                    const SizedBox(height: 20),
                    tabs(
                      Icons.checklist,
                      'Task',
                      1,
                      "${Routes.task}/${widget.projectId}",
                    ),
                    const SizedBox(height: 20),
                    tabs(
                      Icons.attach_file,
                      'Document',
                      2,
                      "${Routes.document}/${widget.projectId}",
                    ),
                    const SizedBox(height: 20),
                    tabs(
                      Icons.receipt_long,
                      'Bill',
                      3,
                      "${Routes.bill}/${widget.projectId}",
                    ),
                    const SizedBox(height: 450),
                    GestureDetector(
                      onTap: () {
                        context.go(Routes.home);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Container(
                          constraints: BoxConstraints(maxWidth: 190),
                          padding: EdgeInsets.symmetric(
                            vertical: 10,
                            // horizontal: 10,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.white),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.arrow_back, color: Colors.white),
                              SizedBox(width: 10),
                              Text(
                                "BACK TO HOME",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget tabs(IconData icon, String label, int index, String route) {
    final selectedTab =
        appRoute.routeInformationProvider.value.uri.toString() == route;
    return MouseRegion(
      onEnter:
          (event) => setState(() {
            selectedIndex = index;
          }),
      onExit:
          (event) => setState(() {
            selectedIndex = -1;
          }),
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: GestureDetector(
          onTap: () {
            context.go(route);
          },
          child: Container(
            decoration: BoxDecoration(
              color:
                  selectedTab
                      ? const Color(0xFF353839)
                      : (selectedIndex == index
                          ? AppColors.primary
                          : Colors.transparent),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              leading: Icon(
                icon,
                // color: selectedTab ? Colors.white : Colors.black,
                color: Colors.white,
              ),
              title: Text(
                label,
                style: TextStyle(
                  // color: selectedTab ? Colors.white : Colors.black,
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: selectedTab ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Future logout() {
  //   return showDialog(
  //       context: context,
  //       builder: (BuildContext context) {
  //         return Expanded(
  //             child: AlertDialog(
  //           title: const Text('Confirm Delete'),
  //           content: const Text('Are you Sure to logout ? '),
  //           actions: [
  //             ElevatedButton(
  //                 onPressed: () {
  //                   setState(() {
  //                     isShow = false;
  //                     context.pop(context);
  //                   });
  //                 },
  //                 child: const Text('Cancel')),
  //             ElevatedButton(
  //                 onPressed: () {
  //                   _signout();
  //                 },
  //                 child: const Text('Confirm'))
  //           ],
  //         ));
  //       });
  // }

  // _signout() {
  //   FBAuth.auth.signOut();
  //   runApp(const MaterialApp(
  //     home: Loginpage(),
  //   ));
  // }
}
