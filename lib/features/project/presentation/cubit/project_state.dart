part of 'project_cubit.dart';

@immutable
class ProjectState {
  final List<ProjectModel> projects;
  final ProjectModel? projectDetail;
  final bool isLoading;
  final String message;

  ProjectState({
    required this.projects,
    required this.projectDetail,
    required this.isLoading,
    required this.message,
  });

  factory ProjectState.initial() {
    return ProjectState(
      projects: [],
      projectDetail: null,
      isLoading: false,
      message: "",
    );
  }

  ProjectState copyWith({
    final List<ProjectModel>? projects,
    final ProjectModel? projectDetail,
    final bool? isLoading,
    final String? message,
  }) {
    return ProjectState(
      projects: projects ?? this.projects,
      projectDetail: projectDetail ?? this.projectDetail,
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
    );
  }
}
