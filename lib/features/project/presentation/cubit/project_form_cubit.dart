import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/entity/transcation_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:flutter/material.dart';

part 'project_form_state.dart';

class ProjectFormCubit extends Cubit<ProjectFormState> {
  final ProjectRepo repo;
  ProjectFormCubit(this.repo) : super(ProjectFormState.initial());

  final titleController = TextEditingController();
  final descController = TextEditingController();
  final addressController = TextEditingController();
  final clientNameController = TextEditingController();
  final clientContactController = TextEditingController();
  final totalAmountController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void initializeForm(ProjectModel? editProject) {
    if (editProject != null) {
      titleController.text = editProject.projectTitle;
      descController.text = editProject.projectDesc;
      addressController.text = editProject.projectAddress;
      clientNameController.text = editProject.clientName;
      clientContactController.text = editProject.clientContact;
      totalAmountController.text = editProject.totalAmount.toString();

      emit(
        state.copyWith(
          projectStatus: editProject.projectStatus,
          selectedAssignUser: editProject.userId,
          dbFile: editProject.projectImg,
          transcations: editProject.transcations,
          completedAt: editProject.completedAt,
        ),
      );
    } else {
      emit(ProjectFormState.initial());
    }
  }

  Future<void> pickFile(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickFile(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> pickFileFromCamera(BuildContext context) async {
    final selectedFile = await ImagePickerService().pickImageNewCamera(
      context,
      useCompressor: true,
    );
    if (selectedFile != null) {
      emit(state.copyWith(selectedFile: selectedFile, message: ""));
    }
  }

  Future<void> viewPickFile(
    String? dbImg,
    BuildContext context,
    String? dbImgExt,
  ) async {
    await viewFile(
      context: context,
      selectedFile: state.selectedFile,
      dbImg: dbImg,
      dbImgExt: dbImgExt,
    );
  }

  void deletPickFile(bool dbImage) {
    if (dbImage) {
      emit(state.copyWith(dbFile: false));
    } else {
      emit(state.copyWith(selectedFile: false));
    }
  }

  void selectStatus(String status) {
    if (status.toLowerCase() == ProjectStatus.active.toLowerCase()) {
      emit(state.copyWith(projectStatus: status));
    } else if (status.toLowerCase() == ProjectStatus.onHold.toLowerCase()) {
      emit(state.copyWith(projectStatus: status));
    } else {
      emit(state.copyWith(projectStatus: status));
    }
  }

  void selectCompletedDate(BuildContext context) async {
    final res = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(2026),
    );

    emit(state.copyWith(completedAt: res));
  }

  void updateSelectedUser(List<String> assignUsers) {
    emit(state.copyWith(selectedAssignUser: assignUsers));
  }

  void submit(ProjectModel? editProject, BuildContext context) async {
    if (state.isLoading) {
      return;
    }
    if (formKey.currentState?.validate() ?? false) {
      num totalAmount = num.tryParse(totalAmountController.text) ?? 0;
      num transactionsTotal = state.transcations.fold(
        0,
        (sum, t) => sum + t.amount,
      );

      if (transactionsTotal > totalAmount) {
        Navigator.pop(context);
        emit(
          state.copyWith(
            message: "Transaction total exceeds the project's total amount.",
            isLoading: false,
          ),
        );
        return; // Stop submission
      }

      emit(state.copyWith(isLoading: true, message: ''));
      try {
        //check file is from upload or database
        final fileUrl =
            state.selectedFile != null
                ? await FirebaseStorageRepo().uploadTaskFile(
                  state.selectedFile!,
                )
                : state.dbFile;

        //assing data
        final project = ProjectModel(
          docId: editProject?.docId ?? "",
          projectTitle: titleController.text,
          projectDesc: descController.text,
          projectAddress: addressController.text,
          userId: state.selectedAssignUser,
          projectStatus: state.projectStatus,
          completedAt: state.completedAt,
          clientName: clientNameController.text,
          clientContact: clientContactController.text,
          totalAmount: num.tryParse(totalAmountController.text) ?? 0,
          transcations: state.transcations,
          projectImg: fileUrl,
          projectImgExt:
              state.selectedFile?.extension ?? editProject?.projectImgExt ?? "",
          projectImgName:
              state.selectedFile?.name ?? editProject?.projectImgName ?? "",
          projectStatusUpdateAt:
              editProject?.projectStatusUpdateAt ?? DateTime.now(),
          createdAt: editProject?.createdAt ?? DateTime.now(),
          createdBy: FBAuth.auth.currentUser?.uid ?? "",
        );

        if (editProject == null) {
          await repo.createProject(project);
          emit(
            state.copyWith(
              isLoading: false,
              message: "New Project created successfully",
            ),
          );
          Navigator.of(context).pop();
        } else {
          await repo.updateProject(project);
          emit(
            state.copyWith(
              isLoading: false,
              message: "Update Task successfully",
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        emit(state.copyWith(isLoading: false, message: e.toString()));
      }
    }
  }

  void addTransaction() {
    emit(
      state.copyWith(
        transcations: [
          ...state.transcations,
          TranscationModel(createdAt: DateTime.now(), amount: 0),
        ],
      ),
    );
  }

  void removeTransaction(int index) {
    emit(
      state.copyWith(
        transcations: List.from(state.transcations)..removeAt(index),
      ),
    );
  }
}
