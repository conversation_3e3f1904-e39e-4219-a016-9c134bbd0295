import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';

part 'project_state.dart';

class ProjectCubit extends Cubit<ProjectState> {
  final ProjectRepo repo;
  ProjectCubit(this.repo) : super(ProjectState.initial());
  StreamSubscription<List<ProjectModel>>? projectsStream;

  void fetchProjects() {
    print("ProjectStream------");
    emit(state.copyWith(isLoading: true));

    projectsStream?.cancel();

    projectsStream = repo.getAllProjects().listen(
      (projects) {
        print("ProjectStream------2");
        emit(state.copyWith(projects: projects, isLoading: false, message: ""));
      },
      onError: (error) {
        print(error.toString());
        emit(state.copyWith(isLoading: false, message: error.toString()));
      },
    );
  }

  deleteProject(String projectId) async {
    emit(state.copyWith(isLoading: true));

    await repo.deleteProject(projectId);
    emit(
      state.copyWith(isLoading: false, message: "Project deleted succesfully."),
    );
  }

  void fetchProjectByIdForDetail(String id) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      final project = state.projects.firstWhere(
        (project) => project.docId == id,
        orElse: null,
      );

      emit(
        state.copyWith(projectDetail: project, isLoading: false, message: ""),
      );
    } catch (e) {
      emit(
        state.copyWith(
          message: "Failed to load project: ${e.toString()}",
          isLoading: false,
        ),
      );
    }
  }

  ProjectModel? fetchProjectById(String id) {
    try {
      final project = state.projects.firstWhere(
        (project) => project.docId == id,
        orElse: null,
      );
      return project;
    } catch (e) {
      return null;
    }
  }
}
