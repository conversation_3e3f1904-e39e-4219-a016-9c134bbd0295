import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/activity/data/firebase_activity_repo.dart';
import 'package:cp_associates/features/bill/data/firebase_bill_repo.dart';
import 'package:cp_associates/features/document/data/firebase_document_repo.dart';
import 'package:cp_associates/features/project/presentation/widgets/desktop_projectlayout.dart';
import 'package:cp_associates/features/project/presentation/widgets/mobile_projectlayout.dart';
import 'package:cp_associates/features/task/data/firebase_task_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_cubit.dart';
import 'package:cp_associates/features/bill/presentation/cubit/bill_cubit.dart';

class ProjectDetailsPage extends StatelessWidget {
  final String projectId;
  final Widget child;

  const ProjectDetailsPage({required this.projectId, required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => ActivityCubit(FirebaseActivityRepo())),
        BlocProvider(create: (_) => TaskCubit(FirebaseTaskRepo())),
        BlocProvider(
          create: (_) => DocumentCubit(documentRepo: FirebaseDocumentRepo()),
        ),
        BlocProvider(create: (_) => BillCubit(repo: FbBillRepo())),
      ],
      child: ResponsiveWid(
        desktop: DesktopProjectLayout(projectId: projectId, child: child),
        mobile: MobileProjectLayout(projectId: projectId),
      ),
    );
  }
}
