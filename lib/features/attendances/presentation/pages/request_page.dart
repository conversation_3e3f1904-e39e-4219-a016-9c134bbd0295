import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequestPage extends StatelessWidget {
  const RequestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Requests")),
      body: BlocBuilder<AttendanceAdminCubit, AttendanceAdminState>(
        builder: (context2, state) {
          if (state.requests.isEmpty) {
            return Center(child: Text("No requests found."));
          }

          return ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            itemCount: state.requests.length,
            separatorBuilder: (_, __) => SizedBox(height: 10),
            itemBuilder: (context, index) {
              final request = state.requests[index];
              final user = context.read<UserCubit>().getUserById(request.uId);

              final statusText = request.active ? "Active" : "Approved";
              final statusColor = request.active ? Colors.orange : Colors.green;

              return InkWell(
                onTap: () async {
                  final selectedDateTime = await context
                      .read<AttendanceAdminCubit>()
                      .selectDateTime(context, request.reqTime);
                  if (selectedDateTime != null) {
                    context.read<AttendanceAdminCubit>().approveRequest(
                      request,
                      selectedDateTime,
                    );
                  }
                },

                child: Card(
                  color: AppColors.containerGreyColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    leading: CircleAvatar(
                      backgroundColor: getColorFromInput(user?.name ?? "?"),
                      child: Text((user?.name ?? "?")[0].toUpperCase()),
                    ),
                    title: Row(
                      children: [
                        Text(user?.name ?? ""),
                        const SizedBox(width: 8),
                        Text(
                          "(${user?.role ?? ""})",
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 6),
                        Text("Requested At: ${request.createdAt.goodTime()}"),
                        Text("Requested Time: ${request.reqTime.goodTime()}"),
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: statusColor.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                statusText,
                                style: TextStyle(
                                  color: statusColor,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    trailing:
                        request.active
                            ? IconButton(
                              icon: Icon(
                                CupertinoIcons.check_mark_circled,
                                color: Colors.green,
                              ),
                              onPressed: () async {
                                final selectedDateTime = await context
                                    .read<AttendanceAdminCubit>()
                                    .selectDateTime(context, request.reqTime);
                                if (selectedDateTime != null) {
                                  context
                                      .read<AttendanceAdminCubit>()
                                      .approveRequest(
                                        request,
                                        selectedDateTime,
                                      );
                                }
                              },
                            )
                            : null,
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
