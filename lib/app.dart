import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/features/admin/data/firebase_admintask_repo.dart';
import 'package:cp_associates/features/admin/presentation/cubit/admin_task_cubit.dart';
import 'package:cp_associates/features/attendances/data/firebase_attendance_repo.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_admin_cubit.dart';
import 'package:cp_associates/features/attendances/presentation/cubit/attendance_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/mastertask/data/firebase_mastertask_repo.dart';
import 'package:cp_associates/features/mastertask/cubit/master_task_cubit.dart';
import 'package:cp_associates/features/project/data/fb_project_repo.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/users/data/firebase_user_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'core/theme/app_theme.dart';
import 'core/widgets/responsive_widget.dart';
import 'features/auth/data/fb_auth_repo.dart';
import 'features/auth/presentation/cubits/auth_cubit.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  final firebaseAuthRepo = FirebaseAuthRepo();
  final firebaseProjectRepo = FirebaseProjectRepo();
  final firebaseUserRepo = FirebaseUserRepo();
  final firebaseMasterTaskRepo = FirebaseMasterTaskRepo();
  final firebaseAdminTaskRepo = FirebaseAdminTaskRepo();
  final firebaseAttendanceRepo = FirebaseAttendanceRepo();

  @override
  @override
  Widget build(BuildContext context) {
    return BlocProvider<AuthCubit>(
      create:
          (_) =>
              AuthCubit(authRepo: firebaseAuthRepo)
                ..checkAuth()
                ..checkUser(),
      child: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          final isAdmin = state.currentUser?.role == "admin";

          return MultiBlocProvider(
            providers: [
              BlocProvider<ProjectCubit>(
                create: (_) => ProjectCubit(firebaseProjectRepo),
              ),
              BlocProvider<MasterTaskCubit>(
                create: (_) => MasterTaskCubit(firebaseMasterTaskRepo),
              ),
              BlocProvider<AdminTaskCubit>(
                create: (_) => AdminTaskCubit(firebaseAdminTaskRepo),
              ),
              BlocProvider<UserCubit>(
                create:
                    (_) =>
                        UserCubit(firebaseUserRepo, firebaseAuthRepo)
                          ..fetchAllUsers(),
              ),

              BlocProvider<AttendanceCubit>(
                create: (_) => AttendanceCubit(firebaseAttendanceRepo),
              ),
              if (isAdmin)
                BlocProvider<AttendanceAdminCubit>(
                  create:
                      (_) =>
                          AttendanceAdminCubit(firebaseAttendanceRepo)
                            ..fetchActiveRequests(),
                ),
            ],
            child: ResponsiveWid(
              mobile: ScreenUtilInit(
                designSize: const Size(430, 932),
                minTextAdapt: true,
                builder:
                    (_, __) => MaterialApp.router(
                      debugShowCheckedModeBanner: false,
                      theme: AppTheme.lightTheme,
                      routerConfig: appRoute,
                    ),
              ),
              desktop: MaterialApp.router(
                debugShowCheckedModeBanner: false,
                theme: AppTheme.lightTheme,
                routerConfig: appRoute,
              ),
            ),
          );
        },
      ),
    );
  }
}
